#!/usr/bin/env python3
"""
Script để chạy cả server và worker cùng lúc.

Usage:
    python run_all.py

Script này sẽ khởi chạy:
1. Huey worker trong background thread
2. FastAPI server trong main thread
"""

import logging
import sys
import threading
import time
import signal
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Configure logging with UTF-8 encoding
import sys
import io

# Set UTF-8 encoding for stdout/stderr on Windows
if sys.platform == "win32":
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('app_combined.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

# Global variables for process management
worker_thread = None
server_process = None
shutdown_event = threading.Event()

def run_worker():
    """Chạy Huey worker trong background thread."""
    try:
        logger.info("Starting Huey worker in background thread...")

        # Import huey instance from queue service
        from services.queue_service import huey
        from huey.consumer import Consumer

        logger.info(f"Queue database: {huey.storage.filename}")
        logger.info("Worker is ready to process tasks")

        # Start the consumer (worker)
        consumer = Consumer(huey)

        # Run worker until shutdown event is set
        while not shutdown_event.is_set():
            try:
                # Run consumer for a short period
                consumer.run(workers=1, periodic=True, initial_delay=0.1, backoff=1.15, max_delay=10.0, utc=True)
            except KeyboardInterrupt:
                break
            except Exception as e:
                logger.error(f"Worker error: {str(e)}")
                time.sleep(1)  # Wait before retrying

        logger.info("Worker thread stopped")

    except Exception as e:
        logger.error(f"Error starting worker: {str(e)}")

def run_server():
    """Chạy FastAPI server."""
    try:
        logger.info("Starting FastAPI server...")

        import uvicorn
        from core.config import get_settings

        settings = get_settings()
        port = int(os.environ.get("PORT", settings.PORT))

        # Run server
        uvicorn.run(
            "main:app",
            host="0.0.0.0",
            port=port,
            reload=False,  # Disable reload when running with worker
            log_level="info"
        )

    except Exception as e:
        logger.error(f"Error starting server: {str(e)}")

def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger.info(f"🛑 Received signal {signum}, shutting down...")
    shutdown_event.set()

    # Stop worker thread
    if worker_thread and worker_thread.is_alive():
        logger.info("🛑 Stopping worker thread...")
        worker_thread.join(timeout=5)

    logger.info("🛑 Shutdown complete")
    sys.exit(0)

def main():
    """Main function để khởi chạy cả server và worker."""
    global worker_thread

    # Setup signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        logger.info("🎯 Starting Red.ai RAG Engine with integrated worker...")

        # Start worker in background thread
        worker_thread = threading.Thread(target=run_worker, daemon=True)
        worker_thread.start()
        logger.info("✅ Worker thread started")

        # Give worker a moment to initialize
        time.sleep(2)

        # Start server in main thread (this will block)
        logger.info("🚀 Starting server in main thread...")
        run_server()

    except KeyboardInterrupt:
        logger.info("🛑 Application stopped by user")
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        logger.error(f"❌ Error in main: {str(e)}")
        signal_handler(signal.SIGTERM, None)

if __name__ == "__main__":
    main()
